import { createApp } from 'vue';
import App from './App.vue';
import { createPinia } from 'pinia';
import i18n, { loadLocaleMessages } from '@/lib/i18n'
import router from './router';
import liff from "@line/liff";
import { applyTheme } from '@/themes/applyTheme';
import { createAppKit } from '@reown/appkit/vue'
// import { mainnet } from '@reown/appkit/networks'
import { defaultNetwork } from './config/networks'
import { Ethers5Adapter } from "@reown/appkit-adapter-ethers5";
import mfTheme from '@/themes/mf';
import gbTheme from '@/themes/gb';

// Use DOMContentLoaded for better Safari compatibility
async function initializeApp() {
    try {
        // Determine theme directly from environment
        const isDevelopment = import.meta.env.DEV;
        const themeNameValue = isDevelopment 
          ? (import.meta.env.VITE_THEME || 'mf')
          : (import.meta.env.VITE_PROD_THEME || 'mf');
        
        const currentTheme = themeNameValue === 'gb' ? gbTheme : mfTheme;
        applyTheme(currentTheme);
        
        await loadLocaleMessages(i18n.global.locale.value, themeNameValue);

        if(themeNameValue === 'gb') {
            await createAppKit({
            adapters: [new Ethers5Adapter()],
            networks: [defaultNetwork],
            projectId: import.meta.env.VITE_APP_KIT_PROJECT_ID,
            metadata: {
              name: 'GooseBox',
              description: 'GooseBox Game',
              url: window.location.origin,
              icons: ['https://avatars.githubusercontent.com/u/37784886'],
            },
          })
        }

        // Now that the translations are loaded, mount the Vue app
        const app = createApp(App);
        app.use(i18n).use(createPinia()).use(router);
        
        // Mount the app first
        const vm = app.mount('#app');

        // App initialization and cleanup is now handled in App.vue

        liff.init({
            liffId: import.meta.env.VITE_LINE_LIFF_ID,
        });

    } catch (error) {
        console.error("Error initializing application:", error);
    }
}

// Initialize app when DOM is ready (better Safari compatibility)
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    // DOM is already loaded
    initializeApp();
}
