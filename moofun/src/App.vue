<!-- App.vue -->
<template>
  <router-view />
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { useGameplayStateManager } from '@/features/gameplay/stores/gameplayStateManager'
import { useNetworkSwitchHandler } from '@/features/auth/composables/useNetworkSwitchHandler'
import { useTheme } from '@/themes/useTheme'

const gameplayStateManager = useGameplayStateManager()
const { themeName } = useTheme()

let networkSwitchHandler = null

onMounted(async () => {
  try {
    // Initialize gameplay state
    gameplayStateManager.enterGameplay()

    // Initialize network switch handler only for gb theme
    if (themeName.value === 'gb') {
      networkSwitchHandler = useNetworkSwitchHandler()
      networkSwitchHandler.handleNetworkEvents()
    }
  } catch (error) {
    console.error('Error initializing app components:', error)
  }
})

onUnmounted(async () => {
  try {
    // Cleanup on app unmount
    await gameplayStateManager.exitGameplay()
  } catch (error) {
    console.error('Error during app cleanup:', error)
  }
})

// Handle app exit
const handleBeforeUnload = async () => {
  try {
    await gameplayStateManager.exitGameplay()
  } catch (error) {
    console.error('Error during exit cleanup:', error)
  }
}

onMounted(() => {
  window.addEventListener('beforeunload', handleBeforeUnload)
})

onUnmounted(() => {
  window.removeEventListener('beforeunload', handleBeforeUnload)
})
</script>

<style>
#app {
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: var(--app-width);
  height: var(--app-height);
  z-index: 1;
  touch-action: manipulation;
}

@media (min-width: 600px) {
  #app {
    max-width: 375px;
  }
}
</style>
